from flask import render_template
from app import app

products = [
    {"id": 1, "name": "تيشيرت قطني", "price": 10, "image": "product1.jpg"},
    {"id": 2, "name": "حذاء رياضي", "price": 25, "image": "product2.jpg"},
    {"id": 3, "name": "ساعة ذكية", "price": 50, "image": "product3.jpg"},
    {"id": 4, "name": "حقيبة يد", "price": 35, "image": "product4.jpg"},
    {"id": 5, "name": "نظارة شمسية", "price": 15, "image": "product5.jpg"},
    {"id": 6, "name": "سماعات لاسلكية", "price": 40, "image": "product6.jpg"},
]

@app.route("/")
def home():
    return render_template("home.html", products=products)
