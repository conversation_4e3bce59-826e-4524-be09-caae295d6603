// Cart functionality
let cart = [];
let cartCount = 0;

// DOM elements
const cartCountElement = document.querySelector('.cart-count');
const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
const cartIcon = document.querySelector('.cart-icon');

// Initialize cart
function initCart() {
    updateCartDisplay();
    
    // Add event listeners to all add to cart buttons
    addToCartButtons.forEach(button => {
        button.addEventListener('click', handleAddToCart);
    });
    
    // Add smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add scroll effect to header
    window.addEventListener('scroll', handleScroll);
    
    // Add animation to product cards when they come into view
    observeProductCards();
}

// Handle add to cart button click
function handleAddToCart(event) {
    const button = event.currentTarget;
    const productId = button.getAttribute('data-id');
    const productCard = button.closest('.product-card');
    const productName = productCard.querySelector('.product-name').textContent;
    const productPrice = productCard.querySelector('.price').textContent;
    
    // Add product to cart
    const product = {
        id: productId,
        name: productName,
        price: productPrice
    };
    
    cart.push(product);
    cartCount++;
    
    // Update cart display
    updateCartDisplay();
    
    // Show feedback animation
    showAddToCartFeedback(button);
    
    // Show notification
    showNotification(`تم إضافة ${productName} إلى السلة!`);
}

// Update cart count display
function updateCartDisplay() {
    cartCountElement.textContent = cartCount;
    
    // Add bounce animation
    cartIcon.style.animation = 'none';
    setTimeout(() => {
        cartIcon.style.animation = 'bounce 0.5s ease';
    }, 10);
}

// Show add to cart feedback animation
function showAddToCartFeedback(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> تم الإضافة';
    button.style.background = '#27ae60';
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.style.background = '';
    }, 1500);
}

// Show notification
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #27ae60;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1001;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Handle scroll effects
function handleScroll() {
    const header = document.querySelector('.header');
    const scrollToTopBtn = document.querySelector('#scrollToTop');
    const scrollY = window.scrollY;

    // Header background effect
    if (scrollY > 100) {
        header.style.background = 'rgba(102, 126, 234, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    } else {
        header.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        header.style.backdropFilter = 'none';
    }

    // Show/hide scroll to top button
    if (scrollY > 300) {
        scrollToTopBtn.classList.add('visible');
    } else {
        scrollToTopBtn.classList.remove('visible');
    }
}

// Observe product cards for animation
function observeProductCards() {
    const productCards = document.querySelectorAll('.product-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    productCards.forEach(card => {
        observer.observe(card);
    });
}

// CTA button functionality
function initCTAButton() {
    const ctaButton = document.querySelector('.cta-button');
    if (ctaButton) {
        ctaButton.addEventListener('click', () => {
            const productsSection = document.querySelector('#products');
            if (productsSection) {
                productsSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }
}

// Add CSS animations
function addAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes bounce {
            0%, 20%, 60%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            80% {
                transform: translateY(-5px);
            }
        }
        
        .notification {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }
    `;
    document.head.appendChild(style);
}

// Scroll to top functionality
function initScrollToTop() {
    const scrollToTopBtn = document.querySelector('#scrollToTop');
    if (scrollToTopBtn) {
        scrollToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initCart();
    initCTAButton();
    initScrollToTop();
    addAnimations();

    // Add loading animation to images
    const images = document.querySelectorAll('.product-image img');
    images.forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
    });
});

// Handle mobile menu (if needed in future)
function toggleMobileMenu() {
    // This can be implemented later for mobile responsiveness
    console.log('Mobile menu toggle');
}
